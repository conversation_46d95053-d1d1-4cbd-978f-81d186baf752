'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  BookOpen,
  Users,
  Briefcase,
  Award,
  Calendar,
  MessageSquare,
  Brain,
  Smartphone
} from 'lucide-react';

const features = [
  {
    icon: BookOpen,
    title: 'Learning Hub',
    description: 'Access comprehensive courses on space technology, satellite engineering, AI, and cybersecurity with interactive content and real-world projects.',
    color: 'bg-blue-100 text-blue-600',
  },
  {
    icon: Users,
    title: 'Mentorship System',
    description: 'Connect with industry experts and experienced professionals for personalized guidance and career development.',
    color: 'bg-green-100 text-green-600',
  },
  {
    icon: Briefcase,
    title: 'Job & Apprenticeship Board',
    description: 'Discover exciting career opportunities in space technology, from internships to senior positions across Africa.',
    color: 'bg-purple-100 text-purple-600',
  },
  {
    icon: Award,
    title: 'Certification Engine',
    description: 'Earn industry-recognized certificates and blockchain-verified credentials to showcase your expertise.',
    color: 'bg-orange-100 text-orange-600',
  },
  {
    icon: Calendar,
    title: 'Events Hub',
    description: 'Join webinars, workshops, job fairs, and networking events to stay connected with the space tech community.',
    color: 'bg-red-100 text-red-600',
  },
  {
    icon: MessageSquare,
    title: 'Community Space',
    description: 'Engage in discussions, share knowledge, and collaborate with fellow space technology enthusiasts.',
    color: 'bg-indigo-100 text-indigo-600',
  },
  {
    icon: Brain,
    title: 'AI-Powered Personalization',
    description: 'Get personalized learning paths, mentor matching, and job recommendations powered by advanced AI.',
    color: 'bg-pink-100 text-pink-600',
  },
  {
    icon: Smartphone,
    title: 'SMS Integration',
    description: 'Stay connected with course reminders, job alerts, and important updates via SMS, ensuring accessibility across Africa.',
    color: 'bg-teal-100 text-teal-600',
  },
];

export function Features() {
  return (
    <section className="py-24 relative overflow-hidden">
      {/* Cosmic Background */}
      <div className="absolute inset-0 cosmic-gradient opacity-50"></div>
      <div className="absolute inset-0">
        {/* Floating cosmic elements */}
        <div className="absolute top-20 left-1/4 w-64 h-64 bg-gradient-to-r from-purple-600/8 to-pink-600/8 rounded-full filter blur-3xl animate-cosmic-drift" />
        <div className="absolute bottom-20 right-1/4 w-48 h-48 bg-gradient-to-r from-blue-600/8 to-purple-600/8 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-3000" />

        {/* Twinkling stars */}
        <div className="absolute top-32 right-1/3 w-1 h-1 bg-blue-400 rounded-full animate-twinkle" />
        <div className="absolute bottom-48 left-1/4 w-1.5 h-1.5 bg-pink-400 rounded-full animate-twinkle animation-delay-2000" />
        <div className="absolute top-64 left-1/2 w-1 h-1 bg-purple-400 rounded-full animate-twinkle animation-delay-4000" />
      </div>

      <div className="relative section-container">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 font-orbitron">
            Everything You Need to
            <span className="text-transparent bg-clip-text nebula-gradient glow-text">
              {" "}Succeed in Space Tech
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Nova provides a comprehensive cosmic ecosystem for learning, networking, and advancing your career
            in space technology and innovation across the galaxy.
          </p>
        </motion.div>

        {/* Features Bento Grid */}
        <div className="bento-grid bento-grid-features">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            const isLarge = index === 0 || index === 3; // Make first and fourth items larger
            const isWide = index === 1; // Make second item wide

            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`bento-item glassmorphic glow-border hover:bg-white/5 transition-all duration-300 group ${
                  isLarge ? 'bento-item-large' : ''
                } ${isWide ? 'bento-item-wide' : ''}`}
              >
                {/* Icon */}
                <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-600 to-pink-600 flex items-center justify-center mb-4 transition-all duration-300">
                  <Icon className="h-6 w-6 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-lg font-semibold text-white mb-3 glow-text font-orbitron">
                  {feature.title}
                </h3>
                <p className="text-gray-300 leading-relaxed text-sm">
                  {feature.description}
                </p>
              </motion.div>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-20"
        >
          <div className="glassmorphic glow-border rounded-3xl p-10 relative overflow-hidden">
            {/* Cosmic background for CTA */}
            <div className="absolute inset-0 nebula-gradient opacity-20 rounded-3xl"></div>
            <div className="relative z-10">
              <h3 className="text-3xl font-bold mb-6 text-white glow-text font-orbitron">
                Ready to Launch Your Cosmic Journey?
              </h3>
              <p className="text-gray-300 mb-8 max-w-2xl mx-auto text-lg leading-relaxed">
                Join thousands of space explorers who are already building the future of cosmic technology in Africa.
                Your galactic adventure begins with a single step into the stars.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <button className="bg-gradient-to-r from-blue-600 to-pink-600 text-white px-10 py-4 rounded-lg font-semibold hover:from-pink-600 hover:to-purple-600 transition-all duration-300">
                  Explore Features
                </button>
                <button className="glassmorphic glow-border text-white px-10 py-4 rounded-lg font-semibold hover:bg-white/5 transition-all duration-300">
                  Book a Demo
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
