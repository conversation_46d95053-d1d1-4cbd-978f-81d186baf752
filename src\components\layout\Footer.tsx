'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Mail, Phone, MapPin, Facebook, Twitter, Linkedin, Instagram } from 'lucide-react';

const footerLinks = {
  platform: [
    { name: 'Learning Hub', href: '/courses' },
    { name: 'Mentorship', href: '/mentorship' },
    { name: 'Job Board', href: '/jobs' },
    { name: 'Events', href: '/events' },
    { name: 'Community', href: '/community' },
  ],
  resources: [
    { name: 'Documentation', href: '/docs' },
    { name: 'API Reference', href: '/api-docs' },
    { name: 'Help Center', href: '/help' },
    { name: 'Blog', href: '/blog' },
    { name: 'Success Stories', href: '/stories' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Careers', href: '/careers' },
    { name: 'Partners', href: '/partners' },
    { name: 'Press Kit', href: '/press' },
    { name: 'Contact', href: '/contact' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
    { name: 'GDPR', href: '/gdpr' },
  ],
};

const socialLinks = [
  { name: 'Facebook', href: '#', icon: Facebook },
  { name: 'Twitter', href: '#', icon: Twitter },
  { name: 'LinkedIn', href: '#', icon: Linkedin },
  { name: 'Instagram', href: '#', icon: Instagram },
];

export function Footer() {
  return (
    <footer className="relative overflow-hidden">
      {/* Cosmic Background */}
      <div className="absolute inset-0 cosmic-gradient"></div>
      <div className="absolute inset-0">
        {/* Floating cosmic elements */}
        <div className="absolute top-20 left-1/4 w-64 h-64 bg-gradient-to-r from-purple-600/8 to-blue-600/8 rounded-full filter blur-3xl animate-cosmic-drift" />
        <div className="absolute bottom-20 right-1/4 w-48 h-48 bg-gradient-to-r from-pink-600/8 to-purple-600/8 rounded-full filter blur-3xl animate-cosmic-drift animation-delay-3000" />

        {/* Twinkling stars */}
        <div className="absolute top-16 right-1/3 w-1 h-1 bg-blue-400 rounded-full animate-twinkle" />
        <div className="absolute bottom-32 left-1/4 w-1.5 h-1.5 bg-pink-400 rounded-full animate-twinkle animation-delay-2000" />
        <div className="absolute top-32 left-1/2 w-1 h-1 bg-purple-400 rounded-full animate-twinkle animation-delay-4000" />
      </div>

      <div className="relative footer-container">
        {/* Main Footer Content */}
        <div className="py-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <Link href="/" className="flex items-center space-x-3 mb-6 group">
                <div className="relative">
                  <Image
                    src="/logo.png"
                    alt="Nova Logo"
                    width={40}
                    height={40}
                    className="transition-transform duration-300 group-hover:scale-110"
                  />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-blue-500 to-pink-600 rounded-full animate-twinkle" />
                </div>
                <span className="text-2xl font-bold text-white glow-text font-orbitron">Nova</span>
              </Link>
              <p className="text-gray-300 mb-8 max-w-md leading-relaxed">
                Empowering Africa's next generation of space technology innovators through
                comprehensive learning, mentorship, and career opportunities.
              </p>

              {/* Contact Info */}
              <div className="space-y-4">
                <div className="flex items-center space-x-3 text-gray-300 hover:text-blue-400 transition-colors duration-300">
                  <Mail className="h-5 w-5" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300 hover:text-blue-400 transition-colors duration-300">
                  <Phone className="h-5 w-5" />
                  <span>+254 700 000 000</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300 hover:text-blue-400 transition-colors duration-300">
                  <MapPin className="h-5 w-5" />
                  <span>Nairobi, Kenya</span>
                </div>
              </div>
            </div>

            {/* Platform Links */}
            <div>
              <h3 className="text-lg font-semibold mb-6 text-white glow-text font-orbitron">Platform</h3>
              <ul className="space-y-3">
                {footerLinks.platform.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-blue-400 transition-all duration-300 hover:glow-text"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Resources Links */}
            <div>
              <h3 className="text-lg font-semibold mb-6 text-white glow-text font-orbitron">Resources</h3>
              <ul className="space-y-3">
                {footerLinks.resources.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-blue-400 transition-all duration-300 hover:glow-text"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company Links */}
            <div>
              <h3 className="text-lg font-semibold mb-6 text-white glow-text font-orbitron">Company</h3>
              <ul className="space-y-3">
                {footerLinks.company.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-blue-400 transition-all duration-300 hover:glow-text"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Legal Links */}
            <div>
              <h3 className="text-lg font-semibold mb-6 text-white glow-text font-orbitron">Legal</h3>
              <ul className="space-y-3">
                {footerLinks.legal.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-blue-400 transition-all duration-300 hover:glow-text"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="py-10 border-t border-white/20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl font-semibold mb-4 text-white glow-text font-orbitron">Stay Updated on Cosmic News</h3>
              <p className="text-gray-300 leading-relaxed">
                Get the latest updates on new cosmic courses, galactic events, and opportunities in space technology across the universe.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-6 py-4 glassmorphic glow-border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-400 text-white placeholder-gray-400 transition-all duration-300"
              />
              <button className="bg-gradient-to-r from-blue-600 to-pink-600 text-white px-8 py-4 rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all duration-300 font-medium">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="py-10 border-t border-white/20">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-300 text-sm mb-4 md:mb-0">
              © 2024 Nova Platform. All rights reserved. Built with ❤️ for Africa's space future.
            </div>

            {/* Social Links */}
            <div className="flex space-x-6">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    className="text-gray-300 hover:text-blue-400 transition-all duration-300"
                    aria-label={social.name}
                  >
                    <Icon className="h-6 w-6" />
                  </a>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
