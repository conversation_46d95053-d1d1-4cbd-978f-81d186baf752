@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

:root {
  /* Refined Dark Galaxy Theme Colors */
  --galaxy-black: #000000;
  --galaxy-dark: #0a0a0f;
  --galaxy-deep: #1a1a2e;
  --galaxy-purple: #2d1b69;
  --galaxy-purple-light: #4c2a85;
  --galaxy-blue: #1e3a8a;
  --galaxy-blue-light: #3b82f6;
  --galaxy-pink: #be185d;
  --galaxy-pink-light: #ec4899;

  /* Refined Cosmic Gradients */
  --cosmic-gradient: linear-gradient(135deg, #000000 0%, #1a1a2e 25%, #2d1b69 50%, #1e3a8a 75%, #000000 100%);
  --nebula-gradient: linear-gradient(45deg, #be185d 0%, #2d1b69 50%, #1e3a8a 100%);
  --aurora-gradient: linear-gradient(90deg, #1e3a8a 0%, #be185d 50%, #2d1b69 100%);

  /* Theme Variables */
  --background: var(--galaxy-black);
  --foreground: #ffffff;
  --text-muted: #a0a0a0;
  --border-glow: rgba(59, 130, 246, 0.3);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-space: 'Space Grotesk', sans-serif;
  --font-orbitron: 'Orbitron', monospace;
}

body {
  background: var(--galaxy-black);
  color: var(--foreground);
  font-family: 'Space Grotesk', var(--font-geist-sans), sans-serif;
  overflow-x: hidden;
}

/* Cosmic Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(0, 212, 255, 0.3); }
  50% { box-shadow: 0 0 40px rgba(0, 212, 255, 0.6), 0 0 60px rgba(255, 0, 110, 0.3); }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes cosmic-drift {
  0% { transform: translateX(-100px) translateY(0px) rotate(0deg); }
  25% { transform: translateX(0px) translateY(-50px) rotate(90deg); }
  50% { transform: translateX(100px) translateY(0px) rotate(180deg); }
  75% { transform: translateX(0px) translateY(50px) rotate(270deg); }
  100% { transform: translateX(-100px) translateY(0px) rotate(360deg); }
}

@keyframes aurora-flow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes parallax-stars {
  0% { transform: translateY(0px); }
  100% { transform: translateY(-100px); }
}

/* Utility Classes */
.cosmic-gradient {
  background: var(--cosmic-gradient);
}

.nebula-gradient {
  background: var(--nebula-gradient);
  background-size: 400% 400%;
  animation: aurora-flow 8s ease-in-out infinite;
}

.aurora-gradient {
  background: var(--aurora-gradient);
  background-size: 200% 200%;
  animation: aurora-flow 6s ease-in-out infinite;
}

.glassmorphic {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.glow-border {
  border: 1px solid var(--border-glow);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.15);
}

.glow-text {
  text-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

.animate-twinkle {
  animation: twinkle 2s ease-in-out infinite;
}

.animate-cosmic-drift {
  animation: cosmic-drift 20s linear infinite;
}

.animation-delay-1000 { animation-delay: 1s; }
.animation-delay-2000 { animation-delay: 2s; }
.animation-delay-3000 { animation-delay: 3s; }
.animation-delay-4000 { animation-delay: 4s; }
.animation-delay-5000 { animation-delay: 5s; }

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar - Dark Galaxy Theme */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--galaxy-dark);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--galaxy-cyan), var(--galaxy-pink));
  border-radius: 6px;
  border: 2px solid var(--galaxy-dark);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--galaxy-pink), var(--galaxy-gold));
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* Star Field Background */
.star-field {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.star-field::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(2px 2px at 20px 30px, #fff, transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
    radial-gradient(1px 1px at 90px 40px, #fff, transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
    radial-gradient(2px 2px at 160px 30px, #fff, transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: parallax-stars 20s linear infinite;
}

/* Parallax Container */
.parallax-container {
  position: relative;
  overflow: hidden;
}

/* Section Margins */
.section-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem;
}

@media (max-width: 768px) {
  .section-container {
    padding: 0 1rem;
  }
}

/* Bento Box Grid Layouts */
.bento-grid {
  display: grid;
  gap: 1.5rem;
  grid-auto-rows: minmax(200px, auto);
}

.bento-grid-features {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.bento-grid-testimonials {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.bento-item {
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.bento-item-large {
  grid-row: span 2;
}

.bento-item-wide {
  grid-column: span 2;
}

.bento-item-tall {
  grid-row: span 2;
}

@media (min-width: 768px) {
  .bento-grid-features {
    grid-template-columns: repeat(4, 1fr);
  }

  .bento-grid-testimonials {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) {
  .bento-grid {
    gap: 2rem;
  }

  .bento-item {
    padding: 2rem;
  }
}

/* Navbar and Footer Margins */
.nav-container, .footer-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem;
}

@media (max-width: 768px) {
  .nav-container, .footer-container {
    padding: 0 1rem;
  }
}
